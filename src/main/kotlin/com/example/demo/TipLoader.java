package com.example.demo;

object TipLoader {
    private const val FILE_NAME = "language.txt"

    fun loadTips(): Map<String, String> {
        val tips = mutableMapOf<String, String>()
        try {
            val stream = TipLoader::class.java.classLoader.getResourceAsStream(FILE_NAME)
            stream?.bufferedReader()?.useLines { lines ->
                    lines.forEach { line ->
                    val parts = line.split("=", ignoreCase = true)
                if (parts.size == 2) {
                    tips[parts[0].trim()] = parts[1].trim()
                }
            }
            }
        } catch (e: IOException) {
            println("Failed to load tips: ${e.message}")
        }
        return tips
    }
}
