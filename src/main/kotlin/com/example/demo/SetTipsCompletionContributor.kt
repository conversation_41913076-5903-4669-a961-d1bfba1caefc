package com.example.demo

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.project.Project
import com.intellij.patterns.ElementPattern
import com.intellij.patterns.PlatformPatterns
import com.intellij.patterns.StandardPatterns
import com.intellij.psi.PsiElement
import com.intellij.util.ProcessingContext
import java.io.File
import java.io.IOException

class SetTipsCompletionContributor : CompletionContributor() {

    init {
        extend(CompletionType.BASIC,
            PlatformPatterns.psiElement().withParent(
                PsiLiteralExpression::class.java),
            object : CompletionProvider<CompletionParameters> {
                override fun addCompletions(parameters: CompletionParameters,
                                            context: ProcessingContext,
                                            result: CompletionResultSet) {
                    val element = parameters.position
                    if (element.text.startsWith("setTips(\"")) {
                        val tips = TipLoader.loadTips()
                        tips.forEach { (key, value) ->
                            result.addElement(
                                LookupElementBuilder.create(key)
                                    .withTailText(" ($value)")
                                    .withIcon(AllIcons.General.Information)
                            )
                        }
                    }
                }
            })
    }
}
