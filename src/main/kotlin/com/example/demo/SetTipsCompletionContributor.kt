package com.example.demo

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.openapi.project.Project
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext
import java.io.File
import java.io.IOException

class SetTipsCompletionContributor : CompletionContributor() {

    init {
        // 匹配字符串字面量
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement()
                .inside(PlatformPatterns.psiElement()
                    .afterLeaf("setTips(")),
            SetTipsCompletionProvider()
        )
    }

    private class SetTipsCompletionProvider() : CompletionProvider<CompletionParameters>() {

        private val entries: Map<String, String> = mutableMapOf()

        override fun addCompletions(
            parameters: CompletionParameters,
            context: ProcessingContext,
            result: CompletionResultSet
        ) {
            val element = parameters.position
            
            // 检查是否在 setTips 调用的字符串参数中
            if (true) {
                val languageEntries = loadLanguageEntries(parameters.editor.project)
                val prefix = getCurrentPrefix(parameters)
                
                // 过滤匹配的条目
                val filteredEntries = if (prefix.isEmpty()) {
                    languageEntries
                } else {
                    entries.filter { (key, value) ->
                        key.startsWith(prefix, ignoreCase = true) || 
                        key.contains(prefix, ignoreCase = true) ||
                        value.contains(prefix, ignoreCase = true)
                    }
                }
                
                filteredEntries.forEach { (key, value) ->
                    val lookupElement = LookupElementBuilder.create(key)
                        .withPresentableText(key)
                        .withTailText(" -> $value", true)
                        .withTypeText("Language Key", true)
                        .withCaseSensitivity(false)
                    result.addElement(lookupElement)
                }
            }
        }


        private fun getCurrentPrefix(parameters: CompletionParameters): String {
            val element = parameters.position
            val text = element.text
            
            // 移除 IntelliJ 的占位符
            return text.replace("IntellijIdeaRulezzz", "").trim()
        }

        private fun loadLanguageEntries(project: Project?) : Map<String, String> {

            try {
                val projectPath = project?.basePath ?: return entries
                val languageFile = File(projectPath, "language.txt")
                
                if (languageFile.exists()) {
                    languageFile.readLines().forEach { line ->
                        val trimmedLine = line.trim()
                        if (trimmedLine.isNotEmpty() && !trimmedLine.startsWith("#") && trimmedLine.contains("=")) {
                            val parts = trimmedLine.split("=", limit = 2)
                            if (parts.size == 2) {
                                val key = parts[0].trim()
                                val value = parts[1].trim()
                                if (key.isNotEmpty() && value.isNotEmpty()) {
                                    val value1 = entries[key] = value
                                }
                            }
                        }
                    }
                }
            } catch (e: IOException) {
                // 忽略IO异常
            }
            return entries
        }
    }
}
