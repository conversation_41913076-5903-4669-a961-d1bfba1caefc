package com.example.demo

import com.intellij.testFramework.fixtures.BasePlatformTestCase
import java.io.File

class SetTipsCompletionTest : BasePlatformTestCase() {

    override fun setUp() {
        super.setUp()
        
        // 创建测试用的language.txt文件
        val languageFile = File(project.basePath, "language.txt")
        languageFile.writeText("""
            welcome=欢迎使用
            login=登录
            logout=退出登录
            username=用户名
            password=密码
            error=错误
            success=成功
        """.trimIndent())
    }

    fun testCompletionInSetTipsCall() {
        // 创建测试文件
        val testFile = myFixture.configureByText("test.js", """
            function test() {
                setTips('<caret>');
            }
        """.trimIndent())

        // 触发代码补全
        val completions = myFixture.completeBasic()
        
        // 验证补全结果
        assertNotNull("应该有补全结果", completions)
        assertTrue("应该包含welcome", completions.any { it.lookupString == "welcome" })
        assertTrue("应该包含login", completions.any { it.lookupString == "login" })
        assertTrue("应该包含username", completions.any { it.lookupString == "username" })
    }

    fun testCompletionWithPrefix() {
        val testFile = myFixture.configureByText("test.js", """
            function test() {
                setTips('log<caret>');
            }
        """.trimIndent())

        val completions = myFixture.completeBasic()
        
        assertNotNull("应该有补全结果", completions)
        assertTrue("应该包含login", completions.any { it.lookupString == "login" })
        assertTrue("应该包含logout", completions.any { it.lookupString == "logout" })
        assertFalse("不应该包含welcome", completions.any { it.lookupString == "welcome" })
    }

    override fun tearDown() {
        // 清理测试文件
        val languageFile = File(project.basePath, "language.txt")
        if (languageFile.exists()) {
            languageFile.delete()
        }
        super.tearDown()
    }
}
